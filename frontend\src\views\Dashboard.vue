<template>
  <div class="dashboard-page">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1 class="welcome-title">
            <span class="greeting">{{ getGreeting() }}</span>
            <span class="username">{{ user?.username || '用户' }}</span>
            <span class="wave">👋</span>
          </h1>
          <p class="welcome-subtitle">
            欢迎回到文件管理工具集，让我们开始高效的文件管理之旅
          </p>
        </div>
        <div class="welcome-illustration">
          <div class="floating-elements">
            <div class="floating-file file-1">📄</div>
            <div class="floating-file file-2">📁</div>
            <div class="floating-file file-3">🖼️</div>
            <div class="floating-file file-4">📊</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <h2 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        数据概览
      </h2>
      <div class="stats-grid">
        <div class="stats-card primary" @click="$router.push('/file-manager')">
          <div class="stats-background">
            <div class="stats-pattern"></div>
          </div>
          <div class="stats-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.file_count }}</div>
            <div class="stats-label">文件总数</div>
            <div class="stats-trend">
              <el-icon><TrendCharts /></el-icon>
              <span>点击查看详情</span>
            </div>
          </div>
        </div>

        <div class="stats-card success" @click="$router.push('/file-manager')">
          <div class="stats-background">
            <div class="stats-pattern"></div>
          </div>
          <div class="stats-icon">
            <el-icon><Folder /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.directory_count }}</div>
            <div class="stats-label">目录总数</div>
            <div class="stats-trend">
              <el-icon><TrendCharts /></el-icon>
              <span>点击查看详情</span>
            </div>
          </div>
        </div>

        <div class="stats-card warning">
          <div class="stats-background">
            <div class="stats-pattern"></div>
          </div>
          <div class="stats-icon">
            <el-icon><DataBoard /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ formatFileSize(stats.total_size) }}</div>
            <div class="stats-label">存储空间</div>
            <div class="stats-trend">
              <el-icon><Monitor /></el-icon>
              <span>存储使用情况</span>
            </div>
          </div>
        </div>

        <div class="stats-card info" @click="$router.push('/operation-logs')">
          <div class="stats-background">
            <div class="stats-pattern"></div>
          </div>
          <div class="stats-icon">
            <el-icon><Operation /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.total_operations }}</div>
            <div class="stats-label">总操作数</div>
            <div class="stats-trend">
              <el-icon><View /></el-icon>
              <span>查看操作日志</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions-section">
      <h2 class="section-title">
        <el-icon><Lightning /></el-icon>
        快捷操作
      </h2>
      <div class="quick-actions-grid">
        <div class="quick-action-card" @click="$router.push('/file-manager')">
          <div class="action-icon">
            <el-icon><FolderOpened /></el-icon>
          </div>
          <div class="action-content">
            <h3>文件管理</h3>
            <p>浏览、上传、下载和管理您的文件</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="$router.push('/batch-rename')">
          <div class="action-icon">
            <el-icon><EditPen /></el-icon>
          </div>
          <div class="action-content">
            <h3>批量重命名</h3>
            <p>使用强大的规则批量重命名文件</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="$router.push('/operation-logs')">
          <div class="action-icon">
            <el-icon><DocumentCopy /></el-icon>
          </div>
          <div class="action-content">
            <h3>操作日志</h3>
            <p>查看详细的操作历史和统计信息</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="$router.push('/profile')">
          <div class="action-icon">
            <el-icon><Setting /></el-icon>
          </div>
          <div class="action-content">
            <h3>个人设置</h3>
            <p>管理您的账户信息和偏好设置</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用指南 -->
    <div class="usage-guide-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon><InfoFilled /></el-icon>
          使用指南
        </h2>
      </div>
      
      <div class="guide-text-content">
        <div class="guide-intro">
          <p class="intro-text">
            欢迎使用文件管理工具集！本系统提供了完整的文件管理解决方案，包括文件上传、批量重命名、下载和操作日志等功能。
          </p>
        </div>
        
        <div class="guide-features">
          <div class="feature-group">
            <h3 class="feature-title">📁 文件管理</h3>
            <ul class="feature-list">
              <li>支持拖拽上传，单个文件最大100MB</li>
              <li>支持批量上传多个文件</li>
              <li>提供文件预览和详细信息查看</li>
              <li>支持文件搜索和筛选功能</li>
            </ul>
          </div>
          
          <div class="feature-group">
            <h3 class="feature-title">✏️ 批量重命名</h3>
            <ul class="feature-list">
              <li>使用正则表达式进行高级重命名</li>
              <li>支持前缀、后缀、序号等多种规则</li>
              <li>提供实时预览功能</li>
              <li>支持智能规则批量处理</li>
            </ul>
          </div>
          
          <div class="feature-group">
            <h3 class="feature-title">📥 文件下载</h3>
            <ul class="feature-list">
              <li>支持单文件直接下载</li>
              <li>支持ZIP打包批量下载</li>
              <li>重命名后可即时下载处理结果</li>
              <li>保持原始文件结构和属性</li>
            </ul>
          </div>
          
          <div class="feature-group">
            <h3 class="feature-title">📋 操作日志</h3>
            <ul class="feature-list">
              <li>详细记录所有文件操作历史</li>
              <li>支持按时间、操作类型筛选</li>
              <li>提供搜索功能快速定位</li>
              <li>便于操作追踪和管理审计</li>
            </ul>
          </div>
        </div>
        
        <div class="guide-tips">
          <div class="tips-header">
            <h3 class="tips-title">💡 使用技巧</h3>
          </div>
          <div class="tips-content">
            <p>• 使用快捷操作区域可以快速访问常用功能</p>
            <p>• 批量重命名时建议先预览结果再执行操作</p>
            <p>• 定期查看操作日志以了解文件变更历史</p>
            <p>• 大文件上传时请保持网络连接稳定</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { apiMethods, formatFileSize } from '@/utils/api'
import type { Stats, OperationLog } from '@/types'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()
const user = authStore.user

const stats = ref<Stats>({
  total_files: 0,
  file_count: 0,
  directory_count: 0,
  total_size: 0,
  storage_used: 0,
  total_operations: 0,
  successful_operations: 0,
  failed_operations: 0
})


const loadStats = async () => {
  try {
    const response = await apiMethods.system.getStats()
    stats.value = response.data.data
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}


const getActionType = (operation_type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    upload: 'success',
    download: 'info',
    delete: 'danger',
    rename: 'warning'
  }
  return typeMap[operation_type] || 'info'
}

const getActionText = (operation_type: string) => {
  const textMap: Record<string, string> = {
    upload: '上传',
    download: '下载',
    delete: '删除',
    rename: '重命名'
  }
  return textMap[operation_type] || operation_type
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了'
  if (hour < 9) return '早上好'
  if (hour < 12) return '上午好'
  if (hour < 14) return '中午好'
  if (hour < 18) return '下午好'
  if (hour < 22) return '晚上好'
  return '夜深了'
}


onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard-page {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%);
  min-height: 100vh;
  position: relative;
}

.dashboard-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: none;
  pointer-events: none;
  z-index: 0;
}

.dashboard-page > * {
  position: relative;
  z-index: 1;
}

/* 欢迎横幅 */
.welcome-banner {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  color: #334155;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-left: 3px solid #3b82f6;
}

.welcome-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(59, 130, 246, 0.01) 100%);
  pointer-events: none;
}

.welcome-banner::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(59,130,246,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  pointer-events: none;
  opacity: 0.3;
}

.welcome-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.welcome-text {
  flex: 1;
}

.welcome-title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.greeting {
  color: #1e293b;
  font-weight: 600;
}

.username {
  color: #3b82f6;
  font-weight: 700;
}

.wave {
  animation: wave 2s ease-in-out infinite;
  transform-origin: 70% 70%;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(20deg); }
  75% { transform: rotate(-10deg); }
}

.welcome-subtitle {
  font-size: 0.95rem;
  margin: 0 0 24px 0;
  color: #64748b;
  line-height: 1.5;
}

.welcome-actions {
  display: flex;
  gap: 16px;
}

.welcome-illustration {
  position: relative;
  width: 120px;
  height: 70px;
}

.floating-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-file {
  position: absolute;
  font-size: 1.2rem;
  animation: dataOrbit 6s linear infinite;
  opacity: 0.8;
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
}

.file-1 {
  top: 15%;
  left: 8%;
  animation-delay: 0s;
}

.file-2 {
  top: 55%;
  left: 55%;
  animation-delay: 1.5s;
}

.file-3 {
  top: 8%;
  right: 15%;
  animation-delay: 3s;
}

.file-4 {
  bottom: 15%;
  left: 35%;
  animation-delay: 4.5s;
}

/* 添加数据流线条 */
.floating-elements::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 3s ease-in-out infinite;
}

.floating-elements::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  border: 1px solid rgba(139, 92, 246, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 3s ease-in-out infinite reverse;
}

@keyframes dataOrbit {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-8px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(0px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-8px) rotate(270deg);
    opacity: 1;
  }
  100% {
    transform: translateY(0px) rotate(360deg);
    opacity: 0.8;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.6;
  }
}

/* 区块标题 */
.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 24px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title .el-icon {
  color: #3b82f6;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 10px;
  max-width: 100%;
}

.stats-card {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  padding: 12px 14px !important;
  position: relative !important;
  overflow: visible !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  min-height: 60px !important;
  border-left: 3px solid transparent !important;
}

.stats-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #e5e7eb;
}

.stats-card.primary {
  border-left-color: #3b82f6;
}

.stats-card.success {
  border-left-color: #10b981;
}

.stats-card.warning {
  border-left-color: #f59e0b;
}

.stats-card.info {
  border-left-color: #06b6d4;
}

.stats-card.primary:hover {
  border-left-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stats-card.success:hover {
  border-left-color: #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.stats-card.warning:hover {
  border-left-color: #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.stats-card.info:hover {
  border-left-color: #06b6d4;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.15);
}

.stats-background {
  display: none;
}

.stats-pattern {
  display: none;
}

.stats-icon {
  width: 36px !important;
  height: 36px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1rem !important;
  position: relative !important;
  top: auto !important;
  right: auto !important;
  z-index: 1 !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
  flex-shrink: 0 !important;
  transition: all 0.3s ease !important;
}

.stats-card.primary .stats-icon {
  background: #3b82f6;
}

.stats-card.success .stats-icon {
  background: #10b981;
}

.stats-card.warning .stats-icon {
  background: #f59e0b;
}

.stats-card.info .stats-icon {
  background: #06b6d4;
}

.stats-card:hover .stats-icon {
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.stats-icon .el-icon {
  font-size: 1rem !important;
  line-height: 1 !important;
  width: 1rem !important;
  height: 1rem !important;
  display: inline-block !important;
}

.stats-content {
  flex: 1;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.stats-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0;
  line-height: 1.2;
}

.stats-label {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 2px;
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 0.65rem;
  color: #9ca3af;
  font-weight: 400;
  opacity: 0.8;
}

.stats-trend .el-icon {
  font-size: 0.65rem;
}

/* 快捷操作 */
.quick-actions-section {
  margin-bottom: 40px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.quick-action-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 60px;
  border-left: 3px solid transparent;
}

.quick-action-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.quick-action-card:nth-child(1) {
  border-left-color: #3b82f6;
}

.quick-action-card:nth-child(2) {
  border-left-color: #10b981;
}

.quick-action-card:nth-child(3) {
  border-left-color: #f59e0b;
}

.quick-action-card:nth-child(4) {
  border-left-color: #06b6d4;
}

.quick-action-card:nth-child(1):hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.quick-action-card:nth-child(2):hover {
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.quick-action-card:nth-child(3):hover {
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.quick-action-card:nth-child(4):hover {
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.15);
}

.action-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

/* 特定卡片的图标颜色 - 与统计卡片保持一致 */
.quick-action-card:nth-child(1) .action-icon {
  background: #3b82f6;
}

.quick-action-card:nth-child(2) .action-icon {
  background: #10b981;
}

.quick-action-card:nth-child(3) .action-icon {
  background: #f59e0b;
}

.quick-action-card:nth-child(4) .action-icon {
  background: #06b6d4;
}

.quick-action-card:hover .action-icon {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.action-content h3 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.2;
}

.action-content p {
  margin: 0;
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.3;
  opacity: 0.8;
}

.action-arrow {
  color: #9ca3af;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.quick-action-card:hover .action-arrow {
  color: #3b82f6;
  transform: translateX(2px);
}

/* 使用指南 */
.usage-guide-section {
  margin-bottom: 40px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e2e8f0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.guide-text-content {
  padding: 0;
}

.guide-intro {
  margin-bottom: 32px;
}

.intro-text {
  font-size: 1rem;
  line-height: 1.6;
  color: #4b5563;
  margin: 0;
  text-align: center;
  font-weight: 400;
}

.guide-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.feature-group {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.feature-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: 6px 0;
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
  position: relative;
  padding-left: 16px;
}

.feature-list li::before {
  content: '•';
  color: #3b82f6;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.guide-tips {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

.tips-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tips-content {
  margin: 0;
}

.tips-content p {
  margin: 8px 0;
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
}


/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-page {
    padding: 16px;
  }
  
  .welcome-banner {
    padding: 12px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .welcome-title {
    font-size: 1.5rem !important;
  }
  
  .welcome-actions {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .stats-card {
    min-height: 65px;
    padding: 12px;
    gap: 12px;
  }
  
  .stats-icon {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }
  
  .stats-icon .el-icon {
    font-size: 1rem !important;
    width: 1rem !important;
    height: 1rem !important;
  }
  
  .stats-value {
    font-size: 1.25rem;
  }
  
  .stats-label {
    font-size: 0.75rem;
  }
  
  .stats-trend {
    font-size: 0.65rem;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .quick-action-card {
    padding: 10px;
    gap: 10px;
    min-height: 55px;
  }

  .action-icon {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .action-content h3 {
    font-size: 0.85rem;
  }

  .action-content p {
    font-size: 0.7rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: 1.25rem !important;
  }
  
  .welcome-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .welcome-actions .el-button {
    width: 100%;
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }
  
  .stats-card {
    padding: 10px;
    gap: 10px;
    min-height: 60px;
  }
  
  .stats-icon {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
  }
  
  .stats-icon .el-icon {
    font-size: 0.875rem !important;
    width: 0.875rem !important;
    height: 0.875rem !important;
  }
  
  .stats-value {
    font-size: 1.1rem;
  }
  
  .stats-label {
    font-size: 0.7rem;
  }
  
  .stats-trend {
    font-size: 0.6rem;
  }
  
  .quick-action-card {
    padding: 8px;
    gap: 8px;
    min-height: 50px;
  }

  .action-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .action-content h3 {
    font-size: 0.8rem;
  }

  .action-content p {
    font-size: 0.65rem;
  }
}
</style>