<template>
  <div class="sidebar" :class="{ collapsed }">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <div class="logo">
        <div class="logo-icon">
          <el-icon><Files /></el-icon>
        </div>
        <transition name="fade">
          <span v-show="!collapsed" class="logo-text">文件管理工具</span>
        </transition>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="sidebar-nav">
      <div class="nav-section">
        <div class="section-title">主要功能</div>
        
        <div class="nav-items">
          <router-link
            v-for="item in mainMenuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{ active: $route.path === item.path }"
          >
            <div class="nav-icon">
              <el-icon><component :is="item.icon" /></el-icon>
            </div>
            <transition name="fade">
              <span v-show="!collapsed" class="nav-text">{{ item.title }}</span>
            </transition>
            <transition name="fade">
              <div v-show="!collapsed && item.badge" class="nav-badge">
                {{ item.badge }}
              </div>
            </transition>
          </router-link>
        </div>
      </div>

      <div class="nav-section">
        <div class="section-title">系统管理</div>
        
        <div class="nav-items">
          <router-link
            v-for="item in systemMenuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{ active: $route.path === item.path }"
          >
            <div class="nav-icon">
              <el-icon><component :is="item.icon" /></el-icon>
            </div>
            <transition name="fade">
              <span v-show="!collapsed" class="nav-text">{{ item.title }}</span>
            </transition>
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <!-- 展开状态的按钮 -->
      <div class="collapse-btn-expanded" @click="handleToggle">
        <el-icon>
          <Fold />
        </el-icon>
      </div>
      <!-- 收缩状态的按钮 -->
      <div class="collapse-btn-collapsed" @click="handleToggle">
        <el-icon>
          <Expand />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import {
  Files,
  DataBoard,
  Upload,
  Edit,
  List,
  Setting,
  Expand,
  Fold
} from '@element-plus/icons-vue'

interface Props {
  collapsed: boolean
}

defineProps<Props>()
const emit = defineEmits<{
  toggle: []
}>()

const route = useRoute()

// 防抖控制
const isToggling = ref(false)

const handleToggle = () => {
  if (isToggling.value) return
  
  isToggling.value = true
  emit('toggle')
  
  // 300ms后重置，与动画时间同步
  setTimeout(() => {
    isToggling.value = false
  }, 300)
}

// 主要功能菜单项
const mainMenuItems = computed(() => [
  {
    path: '/dashboard',
    title: '仪表板',
    icon: 'DataBoard',
    badge: null
  },
  {
    path: '/files',
    title: '文件管理',
    icon: 'Upload',
    badge: null
  },
  {
    path: '/rename',
    title: '批量重命名',
    icon: 'Edit',
    badge: null
  }
])

// 系统管理菜单项
const systemMenuItems = computed(() => [
  {
    path: '/logs',
    title: '操作日志',
    icon: 'List',
    badge: null
  },
  {
    path: '/profile',
    title: '个人设置',
    icon: 'Setting',
    badge: null
  }
])

// 收缩状态下合并所有菜单项
const allMenuItems = computed(() => [
  ...mainMenuItems.value,
  ...systemMenuItems.value
])
</script>

<style scoped>
.sidebar {
  width: 260px;
  background: var(--bg-card);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  min-width: 260px;
  will-change: width;
}

.sidebar.collapsed {
  width: 64px;
  min-width: 64px;
}

/* 确保收缩状态下的内容与顶部汉堡菜单按钮对齐 */
.sidebar.collapsed .sidebar-header {
  padding: var(--space-4) 16px;
}

/* 侧边栏头部 */
.sidebar-header {
  padding: var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  transition: padding var(--sidebar-transition);
  overflow: hidden;
}

.sidebar.collapsed .sidebar-header {
  padding: var(--space-4) 16px;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  white-space: nowrap;
  overflow: hidden;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: var(--primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.logo-text {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
}

/* 导航菜单 */
.sidebar-nav {
  flex: 1;
  padding: var(--space-4) 0;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 收缩状态下的导航区域对齐 */
.sidebar.collapsed .sidebar-nav {
  padding: var(--space-2) 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.nav-section {
  margin-bottom: var(--space-6);
  overflow: hidden;
}

.section-title {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 var(--space-4);
  margin-bottom: var(--space-3);
  white-space: nowrap;
  overflow: hidden;
  text-align: left;
  /* 统一过渡动画 - 确保与侧边栏收缩完全同步 */
  transition: all var(--sidebar-transition);
  position: relative;
}

/* 展开状态下正常显示完整文本 */
.sidebar:not(.collapsed) .section-title {
  /* 保持原有样式 */
}

/* 方案三：完全隐藏方案 - 最符合文件管理工具的实用性需求 */
.sidebar.collapsed .section-title {
  display: none;
}

.sidebar.collapsed .nav-section {
  margin-bottom: 0;
  display: contents;
}

.sidebar.collapsed .nav-items {
  display: contents;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  padding: 0 var(--space-2);
  overflow: hidden;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: background-color 0.15s ease, color 0.15s ease, box-shadow 0.15s ease;
  position: relative;
  min-height: 44px;
  overflow: hidden;
  white-space: nowrap;
}

/* 收缩状态下的普通菜单项对齐 */
.sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 0;
  margin: 0 0 6px 0;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  transition: background-color 0.15s ease, color 0.15s ease, box-shadow 0.15s ease, transform 0.1s ease;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 收缩状态下强制隐藏文字 */
.sidebar.collapsed .nav-text {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.nav-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  transform: translateX(2px);
}

/* 收缩状态下的悬停效果 */
.sidebar.collapsed .nav-item:hover {
  transform: translateY(-1px);
  background: rgba(99, 102, 241, 0.08);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
  border-radius: var(--radius-lg) !important;
}

/* 展开状态下的active样式 */
.sidebar:not(.collapsed) .nav-item.active {
  background: rgb(99 102 241 / 0.1) !important;
  color: var(--primary) !important;
  font-weight: 500 !important;
  transition: background-color 0.15s ease, color 0.15s ease !important;
}

.sidebar:not(.collapsed) .nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--primary);
  border-radius: 0 2px 2px 0;
}

/* 收缩状态下的active样式 */
.sidebar.collapsed .nav-item.active {
  background: var(--primary) !important;
  color: white !important;
  box-shadow: 0 2px 12px rgba(99, 102, 241, 0.3) !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: var(--radius-lg) !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 0 6px 0 !important;
  display: flex !important;
  align-items: center !important;
  position: relative !important;
  transition: background-color 0.15s ease, color 0.15s ease, box-shadow 0.15s ease, transform 0.1s ease !important;
}

.sidebar.collapsed .nav-item.active::before {
  display: none;
}

.sidebar.collapsed .nav-item.active:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
}

.nav-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  flex-shrink: 0;
  transition: transform 0.1s ease;
}

/* 收缩状态下的图标优化 */
.sidebar.collapsed .nav-icon {
  width: 20px;
  height: 20px;
  font-size: var(--text-lg);
  opacity: 1 !important;
  visibility: visible !important;
}

.nav-text {
  flex: 1;
  font-size: var(--text-sm);
  font-weight: 500;
  white-space: nowrap;
  overflow: visible;
  text-overflow: clip;
  min-width: 0;
}

.nav-badge {
  background: var(--danger);
  color: white;
  font-size: var(--text-xs);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: var(--space-4);
  border-top: 1px solid var(--border-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 72px;
  transition: all var(--sidebar-transition);
}

/* 展开状态的按钮 */
.collapse-btn-expanded {
  position: absolute;
  width: calc(100% - var(--space-4) * 2);
  height: 40px;
  background: var(--bg-hover);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--sidebar-transition);
  opacity: 1;
  transform: scale(1);
}

/* 收缩状态的按钮 */
.collapse-btn-collapsed {
  position: absolute;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  border: none;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--sidebar-transition);
  opacity: 0;
  transform: scale(0.8);
}

/* 展开状态 */
.sidebar:not(.collapsed) .collapse-btn-expanded {
  opacity: 1;
  transform: scale(1);
}

.sidebar:not(.collapsed) .collapse-btn-collapsed {
  opacity: 0;
  transform: scale(0.8);
}

/* 收缩状态 */
.sidebar.collapsed .sidebar-footer {
  padding: var(--space-2);
  border-top: none;
  height: 52px;
}

.sidebar.collapsed .collapse-btn-expanded {
  opacity: 0;
  transform: scale(0.8);
}

.sidebar.collapsed .collapse-btn-collapsed {
  opacity: 1;
  transform: scale(1);
}

/* 悬停效果 */
.collapse-btn-expanded:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--primary);
}

.collapse-btn-collapsed:hover {
  background: var(--primary-hover);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

/* 统一fade过渡动画 - 优化快速切换 */
.fade-enter-active {
  transition: opacity 0.15s ease-out;
  transition-delay: 0.1s;
}

.fade-leave-active {
  transition: opacity 0.1s ease-in;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 收缩状态下强制显示所有菜单项 */
.sidebar.collapsed .nav-item,
.sidebar.collapsed .nav-item *,
.sidebar.collapsed .nav-icon,
.sidebar.collapsed .nav-icon * {
  opacity: 1 !important;
  visibility: visible !important;
}

/* 防止快速点击时的布局异常 */
.sidebar.collapsed .nav-item {
  position: relative;
}

.sidebar.collapsed .nav-item * {
  pointer-events: none;
}

.sidebar.collapsed .nav-item {
  pointer-events: auto;
}

/* 滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1001;
  }
  
  .sidebar.collapsed {
    transform: translateX(-100%);
  }
}
</style>
