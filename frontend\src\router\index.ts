import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import Layout from '@/components/Layout.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/Register.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      component: Layout,
      meta: { requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue')
        },
        {
          path: 'files',
          name: 'FileManager',
          component: () => import('@/views/FileManager.vue')
        },
        {
          path: 'file-manager',
          redirect: '/files'
        },
        {
          path: 'rename',
          name: 'BatchRename',
          component: () => import('@/views/BatchRename.vue')
        },
        {
          path: 'batch-rename',
          redirect: '/rename'
        },
        {
          path: 'logs',
          name: 'OperationLogs',
          component: () => import('@/views/OperationLogs.vue')
        },
        {
          path: 'operation-logs',
          redirect: '/logs'
        },
        {
          path: 'profile',
          name: 'Profile',
          component: () => import('@/views/Profile.vue')
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue')
    }
  ]
})

// 路由守卫 - 临时禁用用于测试
router.beforeEach((to, from, next) => {
  // 临时跳过认证检查，直接允许访问所有路由
  next()
})

export default router