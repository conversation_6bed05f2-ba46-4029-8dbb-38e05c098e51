<template>
  <div class="profile-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-text">
          <h1 class="page-title">
            <el-icon><User /></el-icon>
            个人设置
          </h1>
          <p class="page-subtitle">管理您的账户信息和偏好设置</p>
        </div>
        <div class="header-avatar">
          <div class="avatar-container">
            <div class="avatar-circle">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="avatar-info">
              <div class="avatar-name">{{ profileForm.username }}</div>
              <div class="avatar-role">管理员</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="profile-content">
      <!-- 基本信息卡片 -->
      <div class="profile-card">
        <div class="card-header">
          <div class="card-title">
            <el-icon><Edit /></el-icon>
            基本信息
          </div>
          <div class="card-description">更新您的个人资料信息</div>
        </div>
        <div class="card-content">
          <el-form :model="profileForm" :rules="profileRules" ref="profileFormRef" label-position="top">
            <div class="form-grid">
              <el-form-item label="用户名" prop="username">
                <el-input 
                  v-model="profileForm.username" 
                  disabled 
                  size="large"
                  prefix-icon="User"
                >
                  <template #suffix>
                    <el-icon class="disabled-icon"><Lock /></el-icon>
                  </template>
                </el-input>
                <div class="form-help">用户名创建后无法修改</div>
              </el-form-item>
              <el-form-item label="邮箱地址" prop="email">
                <el-input 
                  v-model="profileForm.email" 
                  size="large"
                  prefix-icon="Message"
                  placeholder="请输入邮箱地址"
                />
              </el-form-item>
            </div>
            <div class="form-actions">
              <el-button type="primary" size="large" @click="updateProfile" :loading="updating">
                <el-icon><Check /></el-icon>
                更新资料
              </el-button>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 安全设置卡片 -->
      <div class="profile-card">
        <div class="card-header">
          <div class="card-title">
            <el-icon><Lock /></el-icon>
            安全设置
          </div>
          <div class="card-description">修改您的登录密码以保护账户安全</div>
        </div>
        <div class="card-content">
          <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-position="top">
            <div class="form-grid">
              <el-form-item label="当前密码" prop="old_password">
                <el-input 
                  v-model="passwordForm.old_password" 
                  type="password" 
                  show-password 
                  size="large"
                  prefix-icon="Lock"
                  placeholder="请输入当前密码"
                />
              </el-form-item>
              <el-form-item label="新密码" prop="new_password">
                <el-input 
                  v-model="passwordForm.new_password" 
                  type="password" 
                  show-password 
                  size="large"
                  prefix-icon="Key"
                  placeholder="请输入新密码"
                />
              </el-form-item>
              <el-form-item label="确认新密码" prop="confirm_password">
                <el-input 
                  v-model="passwordForm.confirm_password" 
                  type="password" 
                  show-password 
                  size="large"
                  prefix-icon="Key"
                  placeholder="请再次输入新密码"
                />
              </el-form-item>
            </div>
            <div class="form-actions">
              <el-button type="primary" size="large" @click="changePassword" :loading="changingPassword">
                <el-icon><Refresh /></el-icon>
                修改密码
              </el-button>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 账户统计卡片 -->
      <div class="profile-card">
        <div class="card-header">
          <div class="card-title">
            <el-icon><DataAnalysis /></el-icon>
            账户统计
          </div>
          <div class="card-description">查看您的使用情况和存储统计</div>
        </div>
        <div class="card-content">
          <div class="stats-grid">
            <div class="stat-card primary">
              <div class="stat-background">
                <div class="stat-pattern"></div>
              </div>
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.file_count }}</div>
                <div class="stat-label">文件总数</div>
                <div class="stat-trend">
                  <el-icon><TrendCharts /></el-icon>
                  <span>已管理文件</span>
                </div>
              </div>
            </div>

            <div class="stat-card success">
              <div class="stat-background">
                <div class="stat-pattern"></div>
              </div>
              <div class="stat-icon">
                <el-icon><Folder /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.directory_count }}</div>
                <div class="stat-label">目录总数</div>
                <div class="stat-trend">
                  <el-icon><TrendCharts /></el-icon>
                  <span>文件夹数量</span>
                </div>
              </div>
            </div>

            <div class="stat-card warning">
              <div class="stat-background">
                <div class="stat-pattern"></div>
              </div>
              <div class="stat-icon">
                <el-icon><DataBoard /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatFileSize(stats.total_size) }}</div>
                <div class="stat-label">存储空间</div>
                <div class="stat-trend">
                  <el-icon><Monitor /></el-icon>
                  <span>已使用空间</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { apiMethods, formatFileSize } from '@/utils/api'
import type { Stats } from '@/types'

const authStore = useAuthStore()

const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()
const updating = ref(false)
const changingPassword = ref(false)

const profileForm = reactive({
  username: '',
  email: ''
})

const passwordForm = reactive({
  old_password: '',
  new_password: '',
  confirm_password: ''
})

const stats = ref<Stats>({
  total_files: 0,
  file_count: 0,
  directory_count: 0,
  total_size: 0,
  storage_used: 0,
  total_operations: 0,
  successful_operations: 0,
  failed_operations: 0
})

const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== passwordForm.new_password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const profileRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  old_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const loadProfile = () => {
  if (authStore.user) {
    profileForm.username = authStore.user.username
    profileForm.email = authStore.user.email
  }
}

const loadStats = async () => {
  try {
    const response = await apiMethods.system.getStats()
    stats.value = response.data.data
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const updateProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    updating.value = true
    
    const result = await authStore.updateProfile({
      email: profileForm.email
    })
    
    if (result.success) {
      ElMessage.success('资料更新成功')
    } else {
      ElMessage.error(result.message || '更新失败')
    }
  } catch (error) {
    console.error('更新资料失败:', error)
  } finally {
    updating.value = false
  }
}

const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true
    
    const result = await authStore.changePassword({
      old_password: passwordForm.old_password,
      new_password: passwordForm.new_password
    })
    
    if (result.success) {
      ElMessage.success('密码修改成功')
      // 清空表单
      passwordForm.old_password = ''
      passwordForm.new_password = ''
      passwordForm.confirm_password = ''
    } else {
      ElMessage.error(result.message || '修改密码失败')
    }
  } catch (error) {
    console.error('修改密码失败:', error)
  } finally {
    changingPassword.value = false
  }
}

onMounted(() => {
  loadProfile()
  loadStats()
})
</script>

<style scoped>
.profile-page {
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
  background: #fafbfc;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 32px;
  color: #334155;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(59, 130, 246, 0.01) 100%);
  pointer-events: none;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .el-icon {
  font-size: 1.25rem;
}

.page-subtitle {
  font-size: 0.95rem;
  margin: 0;
  color: #64748b;
  line-height: 1.5;
}

.header-avatar {
  margin-left: 40px;
}

.avatar-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar-circle {
  width: 80px;
  height: 80px;
  background: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.avatar-info {
  text-align: left;
}

.avatar-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: #1e293b;
}

.avatar-role {
  font-size: 0.875rem;
  color: #64748b;
}

/* 内容区域 */
.profile-content {
  display: grid;
  gap: 32px;
}

.profile-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.profile-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #3b82f6;
}

.card-header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 20px;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-title .el-icon {
  color: #3b82f6;
}

.card-description {
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 24px;
}

.card-content {
  padding: 0 20px 20px 20px;
}

/* 表单样式 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.form-help {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 4px;
}

.disabled-icon {
  color: #94a3b8;
}

.form-actions {
  display: flex;
  justify-content: flex-start;
  gap: 16px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  min-height: 80px;
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #3b82f6;
}

.stat-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.02;
  pointer-events: none;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-pattern {
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 2px, transparent 2px),
                    radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 2px, transparent 2px);
  background-size: 40px 40px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  position: relative;
  z-index: 1;
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 2px;
  line-height: 1.2;
}

.stat-label {
  color: #64748b;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 6px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #94a3b8;
  font-size: 0.75rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-page {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .header-avatar {
    margin-left: 0;
  }
  
  .page-title {
    font-size: 1.25rem;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.125rem;
  }
  
  .avatar-circle {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .profile-card {
    margin: 0 -8px;
  }
  
  .card-header,
  .card-content {
    padding-left: 16px;
    padding-right: 16px;
  }
}
</style>