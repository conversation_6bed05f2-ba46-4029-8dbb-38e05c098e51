import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { api } from '@/utils/api'
import type { User, LoginRequest, RegisterRequest } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 设置认证信息
  const setAuth = (userData: User, authToken: string) => {
    user.value = userData
    token.value = authToken
    localStorage.setItem('token', authToken)
    localStorage.setItem('user', JSON.stringify(userData))
  }

  // 清除认证信息
  const clearAuth = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 初始化用户信息
  const initAuth = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
      } catch (error) {
        clearAuth()
      }
    }
  }

  // 登录
  const login = async (credentials: LoginRequest) => {
    loading.value = true
    try {
      const response = await api.post('/auth/login', credentials)
      const { user: userData, token: authToken } = response.data.data
      setAuth(userData, authToken)
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterRequest) => {
    loading.value = true
    try {
      const response = await api.post('/auth/register', userData)
      const { user: newUser, token: authToken } = response.data.data
      setAuth(newUser, authToken)
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '注册失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    clearAuth()
  }

  // 获取用户资料
  const fetchProfile = async () => {
    try {
      const response = await api.get('/users/profile')
      user.value = response.data.data
      localStorage.setItem('user', JSON.stringify(response.data.data))
    } catch (error) {
      console.error('获取用户资料失败:', error)
    }
  }

  // 更新用户资料
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      const response = await api.put('/users/profile', profileData)
      user.value = response.data.data
      localStorage.setItem('user', JSON.stringify(response.data.data))
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '更新失败' 
      }
    }
  }

  // 修改密码
  const changePassword = async (passwordData: { old_password: string; new_password: string }) => {
    try {
      await api.post('/users/change-password', passwordData)
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '修改密码失败' 
      }
    }
  }

  return {
    user: readonly(user),
    token: readonly(token),
    loading: readonly(loading),
    isAuthenticated,
    setAuth,
    clearAuth,
    initAuth,
    login,
    register,
    logout,
    fetchProfile,
    updateProfile,
    changePassword
  }
})