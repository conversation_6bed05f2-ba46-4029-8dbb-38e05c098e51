package controllers

import (
	"encoding/csv"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"file-manager/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type LogController struct {
	db *gorm.DB
}

func NewLogController(db *gorm.DB) *LogController {
	return &LogController{db: db}
}

// GetLogs 获取操作日志列表
func (lc *LogController) GetLogs(c *gin.Context) {
	userID := c.GetUint("user_id")
	page, _ := strconv.Atoi(c.Default<PERSON>y("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "20"))
	action := c.Query("action")
	status := c.Query("status")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	offset := (page - 1) * limit

	query := lc.db.Where("user_id = ?", userID)

	// 添加过滤条件
	if action != "" {
		query = query.Where("action = ?", action)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if startDate != "" {
		query = query.Where("created_at >= ?", startDate)
	}
	if endDate != "" {
		query = query.Where("created_at <= ?", endDate)
	}

	var logs []models.OperationLog
	var total int64

	query.Model(&models.OperationLog{}).Count(&total)
	query.Preload("User").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&logs)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"logs": logs,
			"pagination": gin.H{
				"page":  page,
				"limit": limit,
				"total": total,
				"pages": (total + int64(limit) - 1) / int64(limit),
			},
		},
	})
}

// GetLog 获取单个操作日志
func (lc *LogController) GetLog(c *gin.Context) {
	userID := c.GetUint("user_id")
	logID := c.Param("id")

	var log models.OperationLog
	if err := lc.db.Where("id = ? AND user_id = ?", logID, userID).
		Preload("User").
		First(&log).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Log not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    log,
	})
}

// DeleteLog 删除操作日志
func (lc *LogController) DeleteLog(c *gin.Context) {
	userID := c.GetUint("user_id")
	logID := c.Param("id")

	var log models.OperationLog
	if err := lc.db.Where("id = ? AND user_id = ?", logID, userID).First(&log).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Log not found",
		})
		return
	}

	if err := lc.db.Delete(&log).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete log",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Log deleted successfully",
	})
}

// ExportLogs 导出操作日志
func (lc *LogController) ExportLogs(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req struct {
		Action    string `json:"action"`
		Status    string `json:"status"`
		StartDate string `json:"start_date"`
		EndDate   string `json:"end_date"`
		Format    string `json:"format"` // csv, json
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	query := lc.db.Where("user_id = ?", userID)

	// 添加过滤条件
	if req.Action != "" {
		query = query.Where("action = ?", req.Action)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.StartDate != "" {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("created_at <= ?", req.EndDate)
	}

	var logs []models.OperationLog
	query.Preload("User").Order("created_at DESC").Find(&logs)

	// 根据格式导出
	switch req.Format {
	case "csv":
		lc.exportCSV(c, logs)
	case "json":
		lc.exportJSON(c, logs)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Unsupported export format",
		})
	}
}

// exportCSV 导出CSV格式
func (lc *LogController) exportCSV(c *gin.Context, logs []models.OperationLog) {
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=logs_%s.csv", time.Now().Format("20060102_150405")))

	writer := csv.NewWriter(c.Writer)
	defer writer.Flush()

	// 写入CSV头部
	headers := []string{"ID", "Action", "Resource", "Old Value", "New Value", "Status", "Error Message", "User", "Created At"}
	writer.Write(headers)

	// 写入数据
	for _, log := range logs {
		record := []string{
			strconv.FormatUint(uint64(log.ID), 10),
			log.Action,
			log.Resource,
			log.OldValue,
			log.NewValue,
			log.Status,
			log.ErrorMsg,
			log.User.Username,
			log.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		writer.Write(record)
	}
}

// exportJSON 导出JSON格式
func (lc *LogController) exportJSON(c *gin.Context, logs []models.OperationLog) {
	c.Header("Content-Type", "application/json")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=logs_%s.json", time.Now().Format("20060102_150405")))

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"export_time": time.Now(),
		"total":       len(logs),
		"data":        logs,
	})
}

// CreateLog 创建操作日志（内部使用）
func (lc *LogController) CreateLog(userID uint, action, resource, oldValue, newValue, status, errorMsg string) error {
	log := models.OperationLog{
		Action:   action,
		Resource: resource,
		OldValue: oldValue,
		NewValue: newValue,
		Status:   status,
		ErrorMsg: errorMsg,
		UserID:   userID,
	}

	return lc.db.Create(&log).Error
}

// GetLogStats 获取日志统计信息
func (lc *LogController) GetLogStats(c *gin.Context) {
	userID := c.GetUint("user_id")

	// 统计各种操作的数量
	var stats []struct {
		Action string `json:"action"`
		Count  int64  `json:"count"`
	}

	lc.db.Model(&models.OperationLog{}).
		Where("user_id = ?", userID).
		Select("action, COUNT(*) as count").
		Group("action").
		Find(&stats)

	// 统计成功和失败的操作
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	lc.db.Model(&models.OperationLog{}).
		Where("user_id = ?", userID).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&statusStats)

	// 获取最近7天的操作统计
	var dailyStats []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}

	lc.db.Model(&models.OperationLog{}).
		Where("user_id = ? AND created_at >= ?", userID, time.Now().AddDate(0, 0, -7)).
		Select("DATE(created_at) as date, COUNT(*) as count").
		Group("DATE(created_at)").
		Order("date DESC").
		Find(&dailyStats)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"action_stats": stats,
			"status_stats": statusStats,
			"daily_stats":  dailyStats,
		},
	})
}

// CleanupLogs 清理旧日志
func (lc *LogController) CleanupLogs(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req struct {
		Days int `json:"days" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// 删除指定天数之前的日志
	cutoffDate := time.Now().AddDate(0, 0, -req.Days)
	result := lc.db.Where("user_id = ? AND created_at < ?", userID, cutoffDate).
		Delete(&models.OperationLog{})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to cleanup logs",
			"error":   result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Successfully deleted %d old logs", result.RowsAffected),
		"data": gin.H{
			"deleted_count": result.RowsAffected,
			"cutoff_date":   cutoffDate,
		},
	})
}
