import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import type { ApiResponse } from '@/types'

// 创建axios实例
export const api = axios.create({
  baseURL: '/api/v1',
  timeout: 15000, // 增加超时时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求重试配置
const MAX_RETRY_COUNT = 2
const RETRY_DELAY = 1000

// 重试函数
const retryRequest = (config: any, retryCount = 0): Promise<any> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(api.request(config))
    }, RETRY_DELAY * retryCount)
  })
}

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 直接从localStorage获取token，避免循环依赖
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const authStore = useAuthStore()
    const config = error.config
    
    // 如果请求被取消，不显示错误信息
    if (error.code === 'ECONNABORTED' || error.message === 'canceled') {
      return Promise.reject(error)
    }
    
    // 网络错误重试逻辑
    if (!error.response && config && !config._retryCount) {
      config._retryCount = config._retryCount || 0
      
      if (config._retryCount < MAX_RETRY_COUNT) {
        config._retryCount++
        console.log(`Retrying request (${config._retryCount}/${MAX_RETRY_COUNT}):`, config.url)
        return retryRequest(config, config._retryCount)
      }
    }
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 只有在明确的认证失败时才清除认证信息
          // 避免因网络问题导致的误判
          if (data?.message?.includes('token') ||
              data?.message?.includes('unauthorized') ||
              data?.message?.includes('expired') ||
              data?.message?.includes('Invalid token') ||
              data?.message?.includes('Authorization header required')) {
            authStore.clearAuth()
            window.location.href = '/login'
            ElMessage.error('登录已过期，请重新登录')
          } else {
            // 其他401错误不自动登出，只显示错误信息
            console.warn('Authentication error (not auto-logout):', data?.message)
          }
          break
        case 403:
          ElMessage.error('没有权限访问该资源')
          break
        case 404:
          // 404错误不显示消息，避免干扰用户体验
          console.warn('Resource not found:', error.config?.url)
          break
        case 422:
          ElMessage.error(data.message || '请求参数错误')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data.message || '请求失败')
      }
    } else if (error.request) {
      // 网络错误不显示消息，避免频繁提示
      console.warn('Network error:', error.message)
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API方法封装
export const apiMethods = {
  // 认证相关
  auth: {
    login: (data: any) => api.post('/auth/login', data),
    register: (data: any) => api.post('/auth/register', data),
    refreshToken: (data: any) => api.post('/auth/refresh', data),
    getProfile: () => api.get('/users/profile'),
    updateProfile: (data: any) => api.put('/users/profile', data),
    changePassword: (data: any) => api.post('/users/change-password', data)
  },

  // 文件管理相关
  files: {
    list: (params?: any) => api.get('/files', { params }),
    get: (id: number) => api.get(`/files/${id}`),
    upload: (data: FormData) => api.post('/files/upload', data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
    download: (id: number) => api.get(`/files/${id}/download`, {
      responseType: 'blob'
    }),
    downloadZip: (fileIds: number[]) => api.post('/files/download-zip', { file_ids: fileIds }, {
      responseType: 'blob'
    }),
    delete: (id: number) => api.delete(`/files/${id}`),
    update: (id: number, data: any) => api.put(`/files/${id}`, data),
    search: (data: any) => api.post('/files/search', data),
    batchSearch: (data: any) => api.post('/files/batch-search', data)
  },

  // 目录管理相关
  directories: {
    list: (params?: any) => api.get('/directories', { params }),
    create: (data: any) => api.post('/directories', data),
    get: (id: number) => api.get(`/directories/${id}`),
    update: (id: number, data: any) => api.put(`/directories/${id}`, data),
    delete: (id: number) => api.delete(`/directories/${id}`),
    getFiles: (id: number) => api.get(`/directories/${id}/files`)
  },

  // 批量重命名相关
  rename: {
    preview: (data: any) => api.post('/rename/preview', data),
    execute: (data: any) => api.post('/rename/execute', data),
    getOperations: (params?: any) => api.get('/rename/operations', { params }),
    saveOperation: (data: any) => api.post('/rename/operations', data),
    deleteOperation: (id: number) => api.delete(`/rename/operations/${id}`)
  },

  // 操作日志相关
  logs: {
    list: (params?: any) => api.get('/logs', { params }),
    get: (id: number) => api.get(`/logs/${id}`),
    delete: (id: number) => api.delete(`/logs/${id}`),
    export: (data: any) => api.post('/logs/export', data, {
      responseType: 'blob'
    }),
    getStats: () => api.get('/logs/stats'),
    cleanup: (data: any) => api.post('/logs/cleanup', data)
  },

  // 系统信息相关
  system: {
    getInfo: () => api.get('/system/info'),
    getStats: () => api.get('/system/stats')
  }
}

// 文件下载辅助函数
export const downloadFile = (blob: Blob, filename: string) => {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取文件图标
export const getFileIcon = (extension: string): string => {
  const iconMap: { [key: string]: string } = {
    // 图片
    jpg: 'Picture',
    jpeg: 'Picture',
    png: 'Picture',
    gif: 'Picture',
    bmp: 'Picture',
    svg: 'Picture',
    
    // 文档
    pdf: 'Document',
    doc: 'Document',
    docx: 'Document',
    txt: 'Document',
    rtf: 'Document',
    
    // 表格
    xls: 'Tickets',
    xlsx: 'Tickets',
    csv: 'Tickets',
    
    // 压缩包
    zip: 'Box',
    rar: 'Box',
    '7z': 'Box',
    tar: 'Box',
    gz: 'Box',
    
    // 音频
    mp3: 'Headphones',
    wav: 'Headphones',
    flac: 'Headphones',
    aac: 'Headphones',
    
    // 视频
    mp4: 'VideoPlay',
    avi: 'VideoPlay',
    mov: 'VideoPlay',
    wmv: 'VideoPlay',
    flv: 'VideoPlay',
    
    // 代码
    js: 'Document',
    ts: 'Document',
    html: 'Document',
    css: 'Document',
    json: 'Document',
    xml: 'Document',
    
    // 默认
    default: 'Document'
  }
  
  return iconMap[extension.toLowerCase()] || iconMap.default
}

export default api