<template>
  <div class="file-manager-page">
    <!-- 文件管理横幅 -->
    <div class="file-manager-banner">
      <div class="banner-background">
        <div class="file-pattern"></div>
      </div>
      <div class="banner-content">
        <div class="banner-text">
          <h1 class="banner-title">文件管理中心</h1>
          <p class="banner-subtitle">
            高效管理您的文件，支持批量操作、智能搜索和多种视图模式
          </p>
          <div class="banner-actions">
            <el-button type="primary" size="large" @click="showUploadDialog = true">
              <el-icon><Upload /></el-icon>
              上传文件
            </el-button>
            <el-button size="large" @click="createDirectory">
              <el-icon><FolderAdd /></el-icon>
              新建文件夹
            </el-button>
          </div>
        </div>
        <div class="banner-visual">
          <div class="file-manager-icon">
            <el-icon><FolderOpened /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <h2 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        文件统计
      </h2>
      <div class="stats-grid">
        <div class="stats-card primary">
          <div class="stats-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ files.length }}</div>
            <div class="stats-label">总文件数</div>
            <div class="stats-trend">
              <el-icon><TrendCharts /></el-icon>
              <span>点击查看详情</span>
            </div>
          </div>
        </div>

        <div class="stats-card success">
          <div class="stats-icon">
            <el-icon><Select /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ selectedFiles.length }}</div>
            <div class="stats-label">已选择</div>
            <div class="stats-trend">
              <el-icon><Check /></el-icon>
              <span>已选中文件</span>
            </div>
          </div>
        </div>

        <div class="stats-card warning">
          <div class="stats-icon">
            <el-icon><DataBoard /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ formatFileSize(totalSize) }}</div>
            <div class="stats-label">总大小</div>
            <div class="stats-trend">
              <el-icon><Monitor /></el-icon>
              <span>存储使用情况</span>
            </div>
          </div>
        </div>

        <div class="stats-card info">
          <div class="stats-icon">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ recentFilesCount }}</div>
            <div class="stats-label">最近文件</div>
            <div class="stats-trend">
              <el-icon><Calendar /></el-icon>
              <span>24小时内上传</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions-section">
      <h2 class="section-title">
        <el-icon><Lightning /></el-icon>
        快捷操作
      </h2>
      <div class="quick-actions-grid">
        <div class="quick-action-card" @click="showUploadDialog = true">
          <div class="action-icon">
            <el-icon><Upload /></el-icon>
          </div>
          <div class="action-content">
            <h3>上传文件</h3>
            <p>支持拖拽上传，多文件同时上传</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="createDirectory">
          <div class="action-icon">
            <el-icon><FolderAdd /></el-icon>
          </div>
          <div class="action-content">
            <h3>新建文件夹</h3>
            <p>创建新的文件夹来组织您的文件</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="viewMode = viewMode === 'table' ? 'grid' : 'table'">
          <div class="action-icon">
            <el-icon><Grid /></el-icon>
          </div>
          <div class="action-content">
            <h3>切换视图</h3>
            <p>在列表视图和网格视图之间切换</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="$router.push('/batch-rename')" v-if="selectedFiles.length > 0">
          <div class="action-icon">
            <el-icon><Edit /></el-icon>
          </div>
          <div class="action-content">
            <h3>批量重命名</h3>
            <p>对选中的 {{ selectedFiles.length }} 个文件进行重命名</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件浏览器 -->
    <div class="file-browser-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon><FolderOpened /></el-icon>
          文件浏览器
        </h2>
        <div class="browser-controls">
          <div class="search-container">
            <el-input
              v-model="searchQuery"
              placeholder="搜索文件..."
              @input="handleSearch"
              style="width: 300px"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button @click="showBatchSearchDialog = true" type="primary">
              <el-icon><DocumentCopy /></el-icon>
              批量搜索
            </el-button>
          </div>
          <el-select v-model="filterType" placeholder="文件类型" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="图片" value="jpg,jpeg,png,gif" />
            <el-option label="文档" value="pdf,doc,docx,txt" />
            <el-option label="视频" value="mp4,avi,mov" />
          </el-select>
          <el-button-group>
            <el-button 
              :type="viewMode === 'table' ? 'primary' : ''" 
              @click="viewMode = 'table'"
            >
              <el-icon><List /></el-icon>
            </el-button>
            <el-button 
              :type="viewMode === 'grid' ? 'primary' : ''" 
              @click="viewMode = 'grid'"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>

      <div class="browser-content">
        <!-- 批量操作栏 -->
        <div v-if="selectedFiles.length > 0" class="batch-actions-bar">
          <div class="selected-info">
            <el-icon><Select /></el-icon>
            已选择 {{ selectedFiles.length }} 个文件
          </div>
          <div class="batch-actions">
            <el-button @click="$router.push('/batch-rename')" type="primary">
              <el-icon><Edit /></el-icon>
              批量重命名
            </el-button>
            <el-button @click="batchDelete" type="danger">
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button>
            <el-button @click="selectedFiles = []">
              <el-icon><Close /></el-icon>
              取消选择
            </el-button>
          </div>
        </div>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'" class="table-container">
          <el-table
            :data="files"
            v-loading="loading"
            @selection-change="handleSelectionChange"
            empty-text="暂无文件数据"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="文件名" min-width="200">
              <template #default="{ row }">
                <div class="file-info">
                  <el-icon size="20">
                    <component :is="getFileIcon(getFileExtension(row.original_name))" />
                  </el-icon>
                  <span>{{ row.original_name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="大小" width="100">
              <template #default="{ row }">
                {{ formatFileSize(row.size) }}
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="上传时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" @click="downloadFile(row)">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
                <el-button size="small" type="danger" @click="deleteFile(row)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 网格视图 -->
        <div v-else class="file-grid-container">
          <div v-if="files.length === 0 && !loading" class="empty-state">
            <div class="empty-illustration">
              <div class="empty-icon">
                <el-icon><DocumentRemove /></el-icon>
              </div>
              <div class="empty-content">
                <h3>还没有文件</h3>
                <p>开始上传您的第一个文件</p>
                <el-button type="primary" @click="showUploadDialog = true">
                  立即上传
                </el-button>
              </div>
            </div>
          </div>
          
          <div v-else class="file-grid" v-loading="loading">
            <div 
              v-for="file in files" 
              :key="file.id"
              class="file-item"
              :class="{ selected: selectedFiles.some(f => f.id === file.id) }"
              @click="toggleFileSelection(file)"
            >
              <div class="file-item-icon">
                <el-icon size="48">
                  <component :is="getFileIcon(getFileExtension(file.original_name))" />
                </el-icon>
              </div>
              <div class="file-item-name">{{ file.original_name }}</div>
              <div class="file-item-info">
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
              </div>
              <div class="file-item-actions">
                <el-button size="small" @click.stop="downloadFile(file)" type="primary">
                  <el-icon><Download /></el-icon>
                </el-button>
                <el-button size="small" @click.stop="deleteFile(file)" type="danger">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页和加载更多 -->
        <div v-if="total > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="smartPageSizes"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handleCurrentPageChange"
            background
          />
          
          <!-- 加载更多按钮 -->
          <div v-if="hasMoreFiles" class="load-more-container">
            <el-button
              @click="loadMoreFiles"
              :loading="loadingMore"
              type="primary"
              size="large"
              class="load-more-btn"
            >
              <el-icon v-if="!loadingMore"><ArrowDown /></el-icon>
              {{ loadingMore ? '加载中...' : '加载更多文件' }}
            </el-button>
            <div class="load-more-info">
              已显示 {{ files.length }} / {{ total }} 个文件
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传文件"
      width="700px"
      :close-on-click-modal="false"
      :close-on-press-escape="!isUploading"
      :show-close="!isUploading"
    >
      <div class="upload-dialog-content">
        <!-- 上传区域 -->
        <div class="upload-area" v-if="!isUploading && uploadFileList.length === 0">
          <el-upload
            ref="uploadRef"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :on-progress="handleUploadProgress"
            :on-change="handleFileChange"
            name="files"
            multiple
            drag
            class="modern-upload"
            :auto-upload="false"
          >
            <div class="upload-content">
              <el-icon class="upload-icon"><UploadFilled /></el-icon>
              <div class="upload-text">
                <div class="upload-title">拖拽文件到此处上传</div>
                <div class="upload-subtitle">或 <em>点击选择文件</em></div>
              </div>
            </div>
            <template #tip>
              <div class="upload-tips">
                <div class="tip-item">
                  <el-icon><InfoFilled /></el-icon>
                  <span>支持多文件同时上传</span>
                </div>
                <div class="tip-item">
                  <el-icon><Warning /></el-icon>
                  <span>单个文件不超过100MB</span>
                </div>
                <div class="tip-item">
                  <el-icon><Check /></el-icon>
                  <span>服务端智能去重检测</span>
                </div>
                <div class="tip-item">
                  <el-icon><Check /></el-icon>
                  <span>支持常见文件格式</span>
                </div>
              </div>
            </template>
          </el-upload>
        </div>

        <!-- 统一的文件列表和进度显示 -->
        <div v-if="uploadFileList.length > 0" class="upload-progress-section">
          <!-- 头部信息 -->
          <div class="progress-header">
            <h4>{{ isUploading ? '上传进度' : '已选择文件' }}</h4>
            <div class="header-actions" v-if="!isUploading">
              <el-button size="small" @click="clearFileList">清空</el-button>
              <el-button size="small" type="primary" @click="startUpload">
                开始上传 ({{ uploadFileList.length }} 个文件)
              </el-button>
            </div>
          </div>

          <!-- 上传中显示整体进度 -->
          <div v-if="isUploading" class="uploading-view">
            <!-- 总体进度条 -->
            <div class="overall-progress">
              <div class="progress-info">
                <span class="progress-text">总进度: {{ Math.round(overallProgress) }}%</span>
                <span class="files-count">{{ completedFiles }}/{{ totalFiles }} 个文件</span>
              </div>
              <el-progress
                :percentage="Math.round(overallProgress)"
                :status="uploadStatus"
                :stroke-width="12"
                class="main-progress"
              />
            </div>

            <!-- 当前上传状态 -->
            <div class="current-status">
              <div class="status-info">
                <div class="current-file">
                  <el-icon class="rotating"><Loading /></el-icon>
                  <span>正在上传: {{ currentUploadingFile }}</span>
                </div>
                <div class="upload-stats">
                  <span>上传速度: {{ averageSpeed }}</span>
                  <span>剩余时间: {{ estimatedTime }}</span>
                </div>
              </div>
            </div>

            <!-- 简化的统计信息 -->
            <div class="upload-summary">
              <div class="summary-item success" v-if="completedFiles > 0">
                <el-icon><Check /></el-icon>
                <span>已完成: {{ completedFiles }}</span>
              </div>
              <div class="summary-item error" v-if="errorFiles > 0">
                <el-icon><Close /></el-icon>
                <span>失败: {{ errorFiles }}</span>
              </div>
              <div class="summary-item uploading" v-if="uploadingFiles > 0">
                <el-icon><Loading /></el-icon>
                <span>上传中: {{ uploadingFiles }}</span>
              </div>
            </div>
          </div>

          <!-- 未上传时显示文件列表 -->
          <div v-else class="file-selection-view">
            <!-- 文件统计和控制 -->
            <div class="file-list-controls">
              <div class="file-stats">
                <div class="stat-item">
                  <el-icon><Document /></el-icon>
                  <span>总计: {{ uploadFileList.length }} 个文件</span>
                </div>
                <div class="stat-item">
                  <el-icon><DataBoard /></el-icon>
                  <span>大小: {{ formatTotalSize(uploadFileList) }}</span>
                </div>
              </div>
              <div class="list-toggle" v-if="uploadFileList.length > 5">
                <el-button
                  size="small"
                  @click="showAllFiles = !showAllFiles"
                  :icon="showAllFiles ? ArrowUp : ArrowDown"
                >
                  {{ showAllFiles ? '收起列表' : `展开查看更多文件` }}
                </el-button>
              </div>
            </div>

            <!-- 文件列表 -->
            <div class="file-progress-list" :class="{ 'collapsed': !showAllFiles && uploadFileList.length > 5 }">
              <div
                v-for="(file, index) in displayedFiles"
                :key="index"
                class="file-progress-item ready"
              >
                <div class="file-info">
                  <div class="file-icon">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="file-details">
                    <div class="file-name">{{ file.name }}</div>
                    <div class="file-size">{{ formatFileSize(file.size) }}</div>
                  </div>
                </div>
                <div class="file-actions">
                  <el-button
                    size="small"
                    type="danger"
                    @click="removeFile(index)"
                    :icon="Close"
                    circle
                  />
                </div>
              </div>
              
              <!-- 折叠状态提示 -->
              <div v-if="!showAllFiles && uploadFileList.length > 5" class="collapsed-hint">
                <div class="hint-content">
                  <el-icon><MoreFilled /></el-icon>
                  <span>还有 {{ uploadFileList.length - 5 }} 个文件未显示</span>
                  <el-button size="small" type="primary" @click="showAllFiles = true">
                    查看全部
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="!isUploading && uploadFileList.length === 0"
            @click="showUploadDialog = false"
          >
            取消
          </el-button>
          <el-button
            v-if="isUploading"
            type="danger"
            @click="cancelUpload"
          >
            取消上传
          </el-button>
          <el-button
            v-if="!isUploading && completedFiles > 0"
            type="success"
            @click="finishUpload"
          >
            完成
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量搜索对话框 -->
    <el-dialog
      v-model="showBatchSearchDialog"
      title="批量搜索文件"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="batch-search-content">
        <div class="search-instructions">
          <h4>使用说明</h4>
          <ul>
            <li>每行输入一个搜索关键词</li>
            <li>支持粘贴多个关键词（自动按行分割）</li>
            <li>将搜索所有包含任意关键词的文件</li>
            <li>最多支持100个关键词</li>
          </ul>
        </div>
        
        <el-input
          v-model="batchSearchText"
          type="textarea"
          :rows="10"
          placeholder="请输入搜索关键词，每行一个&#10;例如：&#10;9200190392641700162321&#10;document.pdf&#10;image.jpg"
          @input="handleBatchSearchInput"
        />
        
        <div class="search-stats">
          <span>关键词数量: {{ batchSearchKeywords.length }}</span>
          <span v-if="batchSearchKeywords.length > 100" class="error-text">
            超出限制！最多支持100个关键词
          </span>
        </div>
        
        <div v-if="batchSearchKeywords.length > 0" class="keywords-preview">
          <h5>关键词预览:</h5>
          <div class="keywords-list">
            <el-tag
              v-for="(keyword, index) in batchSearchKeywords.slice(0, 10)"
              :key="index"
              size="small"
              class="keyword-tag"
            >
              {{ keyword }}
            </el-tag>
            <el-tag v-if="batchSearchKeywords.length > 10" size="small" type="info">
              +{{ batchSearchKeywords.length - 10 }} 更多...
            </el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBatchSearchDialog = false">取消</el-button>
          <el-button @click="clearBatchSearch">清空</el-button>
          <el-button
            type="primary"
            @click="executeBatchSearch"
            :disabled="batchSearchKeywords.length === 0 || batchSearchKeywords.length > 100"
          >
            搜索 ({{ batchSearchKeywords.length }} 个关键词)
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload,
  FolderAdd,
  Lightning,
  ArrowRight,
  ArrowDown,
  ArrowUp,
  Edit,
  Grid,
  FolderOpened,
  Search,
  List,
  Select,
  Close,
  Download,
  Delete,
  DocumentRemove,
  UploadFilled,
  InfoFilled,
  Warning,
  Check,
  Picture,
  Document,
  VideoPlay,
  DataAnalysis,
  TrendCharts,
  DataBoard,
  Monitor,
  Clock,
  Calendar,
  Loading,
  DocumentCopy,
  MoreFilled
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { apiMethods, formatFileSize, downloadFile as downloadFileUtil } from '@/utils/api'
import type { FileItem } from '@/types'

const authStore = useAuthStore()

const files = ref<FileItem[]>([])
const loading = ref(false)
const loadingMore = ref(false)
const searchQuery = ref('')
const filterType = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const selectedFiles = ref<FileItem[]>([])
const showUploadDialog = ref(false)
const viewMode = ref<'table' | 'grid'>('table')
const hasMoreFiles = ref(true)

// 批量搜索相关状态
const showBatchSearchDialog = ref(false)
const batchSearchText = ref('')
const batchSearchKeywords = ref<string[]>([])

// 上传进度相关状态
const isUploading = ref(false)
const uploadFileList = ref<any[]>([])
const currentUploadingFile = ref('')
const uploadRef = ref()

// 文件列表显示控制
const showAllFiles = ref(false)

// 上传统计
const completedFiles = computed(() =>
  uploadFileList.value.filter(file => file.status === 'success').length
)
const totalFiles = computed(() => uploadFileList.value.length)
const overallProgress = computed(() => {
  if (totalFiles.value === 0) return 0
  // 基于已完成文件数量计算整体进度
  return (completedFiles.value / totalFiles.value) * 100
})
const uploadStatus = computed(() => {
  if (completedFiles.value === totalFiles.value && totalFiles.value > 0) return 'success'
  if (uploadFileList.value.some(file => file.status === 'error')) return 'exception'
  return undefined
})

// 新增的计算属性
const errorFiles = computed(() =>
  uploadFileList.value.filter(file => file.status === 'error').length
)

const uploadingFiles = computed(() =>
  uploadFileList.value.filter(file => file.status === 'uploading').length
)

const displayedFiles = computed(() => {
  if (showAllFiles.value || uploadFileList.value.length <= 5) {
    return uploadFileList.value
  }
  return uploadFileList.value.slice(0, 5)
})

// 文件选择阶段显示的文件列表
const displayedSelectionFiles = computed(() => {
  if (showAllFiles.value || uploadFileList.value.length <= 3) {
    return uploadFileList.value
  }
  return uploadFileList.value.slice(0, 3)
})

// 计算总文件大小的方法
const formatTotalSize = (fileList: any[]) => {
  const totalBytes = fileList.reduce((sum, file) => sum + (file.size || 0), 0)
  return formatFileSize(totalBytes)
}

// 移除文件的方法
const removeFile = (index: number) => {
  uploadFileList.value.splice(index, 1)
  // 同时更新Element Plus的文件列表
  if (uploadRef.value) {
    const elementFileList = uploadRef.value.uploadFiles
    elementFileList.splice(index, 1)
  }
}

// 上传速度和时间估算
const averageSpeed = ref('0 KB/s')
const estimatedTime = ref('--')
const uploadStartTime = ref(0)
const uploadedBytes = ref(0)

const uploadUrl = computed(() => '/api/v1/files/upload')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${authStore.token}`
}))

// 计算总文件大小
const totalSize = computed(() => {
  return files.value.reduce((total, file) => total + (file.size || 0), 0)
})

// 计算最近文件数量（24小时内）
const recentFilesCount = computed(() => {
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
  return files.value.filter(file => {
    const fileDate = new Date(file.created_at)
    return fileDate > oneDayAgo
  }).length
})

// 智能分页大小计算
const smartPageSizes = computed(() => {
  const totalCount = total.value
  const baseSizes = [10, 20, 50]
  
  if (totalCount <= 50) {
    // 小数据量：提供基础选项
    return baseSizes
  } else if (totalCount <= 200) {
    // 中等数据量：添加100选项
    return [...baseSizes, 100]
  } else if (totalCount <= 500) {
    // 大数据量：添加200和500选项
    return [...baseSizes, 100, 200, 500]
  } else if (totalCount <= 1000) {
    // 超大数据量：添加更多选项，包括"全部"
    return [...baseSizes, 100, 200, 500, 1000, totalCount]
  } else if (totalCount <= 5000) {
    // 海量数据：提供更灵活的选项
    return [...baseSizes, 100, 200, 500, 1000, 2000, Math.ceil(totalCount / 2), totalCount]
  } else {
    // 超海量数据：提供分层选项，避免性能问题
    const quarter = Math.ceil(totalCount / 4)
    const half = Math.ceil(totalCount / 2)
    return [...baseSizes, 100, 200, 500, 1000, 2000, quarter, half, totalCount]
  }
})

const getFileExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : ''
}

const getFileIcon = (extension: string) => {
  const ext = extension?.toLowerCase() || ''
  if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext)) {
    return Picture
  } else if (['pdf', 'doc', 'docx', 'txt', 'md'].includes(ext)) {
    return Document
  } else if (['mp4', 'avi', 'mov', 'mkv', 'webm'].includes(ext)) {
    return VideoPlay
  } else if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(ext)) {
    return Document // 使用Document作为音频文件的图标
  } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
    return Document
  } else if (['js', 'ts', 'vue', 'html', 'css', 'json', 'xml'].includes(ext)) {
    return Document
  }
  return Document
}

const loadFiles = async (append = false) => {
  try {
    if (append) {
      loadingMore.value = true
    } else {
      loading.value = true
      files.value = []
    }
    
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value
    }
    
    const response = await apiMethods.files.list(params)
    const newFiles = response.data.data.files
    
    if (append) {
      files.value = [...files.value, ...newFiles]
    } else {
      files.value = newFiles
    }
    
    total.value = response.data.data.pagination.total
    hasMoreFiles.value = files.value.length < total.value
  } catch (error) {
    console.error('加载文件列表失败:', error)
    ElMessage.error('加载文件列表失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

const loadMoreFiles = async () => {
  if (loadingMore.value || !hasMoreFiles.value) return
  currentPage.value++
  await loadFiles(true)
}

const handleSearch = () => {
  // 搜索时重置到第一页
  currentPage.value = 1
  loadFiles()
}

const handlePageSizeChange = (newPageSize: number) => {
  // 保存当前滚动位置
  const currentScrollY = window.scrollY
  
  pageSize.value = newPageSize
  currentPage.value = 1
  loadFiles().then(() => {
    // 数据加载完成后，恢复滚动位置
    nextTick(() => {
      window.scrollTo(0, currentScrollY)
    })
  })
}

const handleCurrentPageChange = (newPage: number) => {
  // 保存当前滚动位置
  const currentScrollY = window.scrollY
  
  currentPage.value = newPage
  loadFiles(false).then(() => {
    // 数据加载完成后，恢复滚动位置
    nextTick(() => {
      window.scrollTo(0, currentScrollY)
    })
  })
}

const handleSelectionChange = (selection: FileItem[]) => {
  selectedFiles.value = selection
}

const downloadFile = async (file: FileItem) => {
  try {
    const response = await apiMethods.files.download(file.id)
    downloadFileUtil(response.data, file.original_name)
    ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

const deleteFile = async (file: FileItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.original_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await apiMethods.files.delete(file.id)
    ElMessage.success('文件删除成功')
    loadFiles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败')
    }
  }
}

const createDirectory = async () => {
  try {
    const { value: name } = await ElMessageBox.prompt(
      '请输入文件夹名称',
      '新建文件夹',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )
    
    if (name) {
      await apiMethods.directories.create({ name })
      ElMessage.success('文件夹创建成功')
      loadFiles()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('创建文件夹失败:', error)
      ElMessage.error('创建文件夹失败')
    }
  }
}

const beforeUpload = (file: File) => {
  const maxSize = 100 * 1024 * 1024 // 100MB
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过100MB')
    return false
  }
  return true
}


const formatDate = (date: string) => {
  return new Date(date).toLocaleString()
}

// 切换文件选择状态
const toggleFileSelection = (file: FileItem) => {
  const index = selectedFiles.value.findIndex(f => f.id === file.id)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(file)
  }
}

// 批量删除文件
const batchDelete = async () => {
  if (selectedFiles.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？此操作不可撤销。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 批量删除API调用
    const deletePromises = selectedFiles.value.map(file =>
      apiMethods.files.delete(file.id)
    )
    
    await Promise.all(deletePromises)
    ElMessage.success(`成功删除 ${selectedFiles.value.length} 个文件`)
    selectedFiles.value = []
    loadFiles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 新的上传相关方法
const handleFileChange = (file: any, fileList: any[]) => {
  // 当文件列表改变时更新我们的文件列表
  uploadFileList.value = fileList.map(item => ({
    name: item.name,
    size: item.size,
    status: 'ready',
    errorMessage: '',
    file: item.raw
  }))
}

const clearFileList = () => {
  uploadFileList.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const startUpload = async () => {
  if (uploadFileList.value.length === 0) return
  
  isUploading.value = true
  uploadStartTime.value = Date.now()
  uploadedBytes.value = 0
  
  try {
    // 逐个上传文件
    for (let i = 0; i < uploadFileList.value.length; i++) {
      const fileItem = uploadFileList.value[i]
      currentUploadingFile.value = fileItem.name
      fileItem.status = 'uploading'
      
      await uploadSingleFile(fileItem, i)
    }
    
    // 所有文件上传完成
    const successCount = uploadFileList.value.filter(f => f.status === 'success').length
    const errorCount = uploadFileList.value.filter(f => f.status === 'error').length
    
    if (successCount > 0) {
      ElMessage.success(`成功上传 ${successCount} 个文件${errorCount > 0 ? `，${errorCount} 个文件失败` : ''}`)
      loadFiles() // 刷新文件列表
    }
    
    if (errorCount === uploadFileList.value.length) {
      ElMessage.error('所有文件上传失败')
    }
    
  } catch (error) {
    console.error('上传过程中出错:', error)
    ElMessage.error('上传过程中出现错误')
  } finally {
    isUploading.value = false
    currentUploadingFile.value = ''
  }
}

const uploadSingleFile = (fileItem: any, index: number): Promise<void> => {
  return new Promise((resolve, reject) => {
    const formData = new FormData()
    formData.append('files', fileItem.file)
    
    const xhr = new XMLHttpRequest()
    
    // 上传进度监听 - 只更新整体进度，不更新单个文件进度
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        // 更新总体进度
        updateOverallProgress()
      }
    })
    
    // 上传完成监听
    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText)
          if (response.success) {
            fileItem.status = 'success'
          } else {
            fileItem.status = 'error'
            fileItem.errorMessage = response.message || '上传失败'
          }
        } catch (error) {
          fileItem.status = 'error'
          fileItem.errorMessage = '响应解析失败'
        }
      } else {
        fileItem.status = 'error'
        fileItem.errorMessage = `HTTP ${xhr.status}`
      }
      resolve()
    })
    
    // 上传错误监听
    xhr.addEventListener('error', () => {
      fileItem.status = 'error'
      fileItem.errorMessage = '网络错误'
      resolve()
    })
    
    // 发送请求
    xhr.open('POST', uploadUrl.value)
    xhr.setRequestHeader('Authorization', uploadHeaders.value.Authorization)
    xhr.send(formData)
  })
}

const updateOverallProgress = () => {
  // 计算平均速度和剩余时间
  const elapsed = (Date.now() - uploadStartTime.value) / 1000
  const completedFiles = uploadFileList.value.filter(f => f.status === 'success').length
  const remainingFiles = uploadFileList.value.length - completedFiles
  
  if (elapsed > 0 && completedFiles > 0) {
    const avgTimePerFile = elapsed / completedFiles
    const estimatedRemainingTime = avgTimePerFile * remainingFiles
    estimatedTime.value = formatTime(estimatedRemainingTime)
    
    // 计算总体速度 - 基于已完成文件的总大小
    const completedBytes = uploadFileList.value
      .filter(f => f.status === 'success')
      .reduce((sum, file) => sum + file.size, 0)
    const speed = completedBytes / elapsed
    averageSpeed.value = formatSpeed(speed)
  }
}

const formatSpeed = (bytesPerSecond: number): string => {
  if (bytesPerSecond < 1024) return `${Math.round(bytesPerSecond)} B/s`
  if (bytesPerSecond < 1024 * 1024) return `${Math.round(bytesPerSecond / 1024)} KB/s`
  return `${Math.round(bytesPerSecond / (1024 * 1024))} MB/s`
}

const formatTime = (seconds: number): string => {
  if (seconds < 60) return `${Math.round(seconds)}秒`
  if (seconds < 3600) return `${Math.round(seconds / 60)}分钟`
  return `${Math.round(seconds / 3600)}小时`
}

const cancelUpload = () => {
  isUploading.value = false
  currentUploadingFile.value = ''
  
  // 将正在上传的文件标记为取消
  uploadFileList.value.forEach(file => {
    if (file.status === 'uploading') {
      file.status = 'error'
      file.errorMessage = '用户取消'
    }
  })
  
  ElMessage.info('上传已取消')
}

const finishUpload = () => {
  showUploadDialog.value = false
  uploadFileList.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 重写原有的上传处理方法
const handleUploadSuccess = (response: any, file: any, fileList: any[]) => {
  // 这个方法现在由我们的自定义上传逻辑处理，这里保留为空
}

const handleUploadError = (error: any, file: any, fileList: any[]) => {
  // 这个方法现在由我们的自定义上传逻辑处理，这里保留为空
}

// 上传进度处理
const handleUploadProgress = (event: any, file: any, fileList: any[]) => {
  // 这个方法现在由我们的自定义上传逻辑处理，这里保留为空
}

// 批量搜索相关方法
const handleBatchSearchInput = () => {
  // 处理批量搜索输入，按行分割关键词
  const keywords = batchSearchText.value
    .split('\n')
    .map(keyword => keyword.trim())
    .filter(keyword => keyword.length > 0)
  
  batchSearchKeywords.value = keywords
}

const clearBatchSearch = () => {
  batchSearchText.value = ''
  batchSearchKeywords.value = []
}

const executeBatchSearch = async () => {
  if (batchSearchKeywords.value.length === 0) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  if (batchSearchKeywords.value.length > 100) {
    ElMessage.error('关键词数量不能超过100个')
    return
  }
  
  try {
    loading.value = true
    showBatchSearchDialog.value = false
    
    // 修改后端API调用，支持批量搜索
    const response = await apiMethods.files.batchSearch({
      keywords: batchSearchKeywords.value,
      page: 1,
      limit: pageSize.value
    })
    
    files.value = response.data.data.files
    total.value = response.data.data.pagination.total
    currentPage.value = 1
    hasMoreFiles.value = files.value.length < total.value
    
    // 将批量搜索关键词合并为单个搜索查询以便显示
    searchQuery.value = `批量搜索(${batchSearchKeywords.value.length}个关键词)`
    
    ElMessage.success(`找到 ${files.value.length} 个匹配的文件`)
    
  } catch (error) {
    console.error('批量搜索失败:', error)
    ElMessage.error('批量搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadFiles()
  
  // 强制设置标题样式 - 使用多种选择器确保找到元素
  nextTick(() => {
    const selectors = [
      '.file-manager-banner .banner-title',
      '.file-manager-page .banner-title',
      '.file-manager-page h1',
      '.file-manager-banner h1',
      '.title-content h1',
      'h1.banner-title',
      'h1'
    ]
    
    let titleElement: HTMLElement | null = null
    
    // 尝试多个选择器找到标题元素
    for (const selector of selectors) {
      titleElement = document.querySelector(selector) as HTMLElement
      if (titleElement && titleElement.textContent?.includes('文件管理中心')) {
        break
      }
    }
    
    if (titleElement) {
      // 强制设置所有可能影响字体大小的CSS属性
      const styles = {
        'font-size': '1.25rem',
        'font-weight': '600',
        'line-height': '1.2',
        'margin': '0',
        'margin-top': '0',
        'margin-bottom': '0',
        'margin-left': '0',
        'margin-right': '0',
        'padding': '0',
        'padding-top': '0',
        'padding-bottom': '0',
        'padding-left': '0',
        'padding-right': '0',
        'letter-spacing': '-0.025em',
        'color': '#1e293b',
        'border': 'none',
        'background': 'none',
        'text-decoration': 'none',
        'outline': 'none',
        'box-shadow': 'none',
        'transform': 'none',
        'zoom': '1',
        'scale': '1'
      }
      
      // 设置每个样式属性
      Object.entries(styles).forEach(([property, value]) => {
        titleElement!.style.setProperty(property, value, 'important')
      })
      
      // 确保类名正确
      titleElement.className = 'banner-title'
      
      console.log('文件管理中心标题样式已强制设置:', titleElement.style.fontSize)
    } else {
      console.warn('未找到文件管理中心标题元素')
    }
  })
  
  // 延迟再次检查，确保样式生效
  setTimeout(() => {
    const titleElement = document.querySelector('.file-manager-banner .banner-title') as HTMLElement
    if (titleElement) {
      titleElement.style.setProperty('font-size', '1.25rem', 'important')
      console.log('延迟检查 - 文件管理中心标题样式:', titleElement.style.fontSize)
    }
  }, 100)
})
</script>

<style scoped>
.file-manager-page {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
  background: #fafbfc;
  min-height: 100vh;
}

/* 文件管理横幅 - 简约现代风格 */
.file-manager-banner {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  color: #334155;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.banner-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.03;
}

.file-pattern {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, #64748b 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, #64748b 1px, transparent 1px);
  background-size: 80px 80px, 120px 120px;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.banner-text {
  flex: 1;
}

.file-manager-banner .banner-title,
.file-manager-page .banner-title,
h1.banner-title,
.file-manager-banner h1,
.file-manager-page h1,
.banner-title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 0 8px 0 !important;
  color: #1e293b !important;
  letter-spacing: -0.025em !important;
  line-height: 1.2 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  text-decoration: none !important;
  outline: none !important;
  box-shadow: none !important;
  transform: none !important;
  zoom: 1 !important;
  scale: 1 !important;
}

.banner-subtitle {
  font-size: 0.95rem;
  margin: 0 0 16px 0;
  color: #64748b;
  line-height: 1.4;
  font-weight: 400;
}

.banner-actions {
  display: flex;
  gap: 12px;
}

.banner-visual {
  position: relative;
  width: 80px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-manager-icon {
  width: 48px;
  height: 48px;
  background: #3b82f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.file-manager-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

/* 仅在支持hover的设备上启用脉冲动画 */
@media (hover: hover) and (prefers-reduced-motion: no-preference) {
  .file-manager-banner:hover .file-manager-icon {
    animation: iconPulse 3s ease-in-out infinite;
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }
}

/* 区块标题 - 简约现代风格 */
.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 28px 0;
  display: flex;
  align-items: center;
  gap: 10px;
  letter-spacing: -0.025em;
}

.section-title .el-icon {
  color: #3b82f6;
  font-size: 1.2rem;
}

/* 统计卡片 - 紧凑现代风格 */
.stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 10px;
  max-width: 100%;
}

.stats-card {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  padding: 8px 10px !important;
  position: relative !important;
  overflow: visible !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  min-height: 60px !important;
  border-left: 3px solid transparent !important;
}

.stats-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #e5e7eb;
}

.stats-card.primary {
  border-left-color: #3b82f6;
}

.stats-card.success {
  border-left-color: #10b981;
}

.stats-card.warning {
  border-left-color: #f59e0b;
}

.stats-card.info {
  border-left-color: #06b6d4;
}

.stats-card.primary:hover {
  border-left-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stats-card.success:hover {
  border-left-color: #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.stats-card.warning:hover {
  border-left-color: #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.stats-card.info:hover {
  border-left-color: #06b6d4;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.15);
}


.stats-icon {
  width: 36px !important;
  height: 36px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1rem !important;
  position: relative !important;
  top: auto !important;
  right: auto !important;
  z-index: 1 !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
  flex-shrink: 0 !important;
  transition: all 0.3s ease !important;
  overflow: visible !important;
}

.stats-card.primary .stats-icon {
  background: #3b82f6;
}

.stats-card.success .stats-icon {
  background: #10b981;
}

.stats-card.warning .stats-icon {
  background: #f59e0b;
}

.stats-card.info .stats-icon {
  background: #06b6d4;
}

.stats-card:hover .stats-icon {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.stats-icon .el-icon {
  font-size: 1rem !important;
  line-height: 1 !important;
  width: 1rem !important;
  height: 1rem !important;
  display: inline-block !important;
}

.stats-content {
  flex: 1;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1px;
}

.stats-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0;
  line-height: 1.1;
}

.stats-label {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 1px;
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 0.625rem;
  color: #9ca3af;
  font-weight: 400;
  opacity: 0.8;
}

.stats-trend .el-icon {
  font-size: 0.625rem;
}

/* 快捷操作 - 现代化卡片设计 */
.quick-actions-section {
  margin-bottom: 32px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.quick-action-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: 80px;
  position: relative;
  overflow: hidden;
}

.quick-action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 0;
}

.quick-action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: rgba(59, 130, 246, 0.3);
}

.quick-action-card:hover::before {
  opacity: 1;
}

.action-icon {
  width: 36px !important;
  height: 36px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1rem !important;
  position: relative !important;
  top: auto !important;
  right: auto !important;
  z-index: 1 !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
  flex-shrink: 0 !important;
  transition: all 0.3s ease !important;
  overflow: visible !important;
}

.quick-action-card:hover .action-icon {
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.action-icon .el-icon {
  font-size: 1rem !important;
  line-height: 1 !important;
  width: 1rem !important;
  height: 1rem !important;
  display: inline-block !important;
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
  z-index: 1;
}

.action-content h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.action-content p {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.4;
  opacity: 0.9;
}

.action-arrow {
  color: #94a3b8;
  transition: all 0.4s ease;
  font-size: 1.125rem;
  position: relative;
  z-index: 1;
}

.quick-action-card:hover .action-arrow {
  color: #3b82f6;
  transform: translateX(4px);
}

/* 特定卡片的图标颜色 - 与统计卡片保持一致 */
.quick-action-card:nth-child(1) .action-icon {
  background: #3b82f6;
}

.quick-action-card:nth-child(2) .action-icon {
  background: #10b981;
}

.quick-action-card:nth-child(3) .action-icon {
  background: #f59e0b;
}

.quick-action-card:nth-child(4) .action-icon {
  background: #06b6d4;
}

.quick-action-card:hover .action-icon {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

/* 文件浏览器 - 简约现代风格 */
.file-browser-section {
  margin-bottom: 48px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 28px;
}

.section-header h2 {
  margin: 0;
}

.browser-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.browser-content {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 批量操作栏 - 简约现代风格 */
.batch-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 28px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.selected-info {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
}

.batch-actions {
  display: flex;
  gap: 12px;
}

/* 表格容器 - 简约现代风格 */
.table-container {
  padding: 28px;
  max-height: 600px;
  overflow-y: auto;
  /* 添加硬件加速优化 */
  transform: translateZ(0);
  will-change: scroll-position;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  text-align: left; /* 确保文件名左对齐 */
}

.file-info span {
  text-align: left; /* 确保文件名文本左对齐 */
  flex: 1;
  min-width: 0;
  word-break: break-word;
}

/* 网格视图 - 简约现代风格 */
.file-grid-container {
  padding: 28px;
  max-height: 600px;
  overflow-y: auto;
  /* 添加硬件加速优化 */
  transform: translateZ(0);
  will-change: scroll-position;
}

.empty-state {
  padding: 48px 24px;
  text-align: center;
}

.empty-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: var(--bg-tertiary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--text-tertiary);
}

.empty-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.empty-content p {
  margin: 0 0 24px 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
}

.file-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.file-item:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.file-item.selected {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.04);
}

.file-item-icon {
  margin-bottom: 16px;
}

.file-item-name {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
  word-break: break-word;
  line-height: 1.4;
  text-align: left;
  font-size: 0.9rem;
}

.file-item-info {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  font-size: 0.875rem;
  gap: 8px;
}

.file-size {
  font-weight: 500;
  color: #64748b;
}

.file-item-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* 分页 - 简约现代风格 */
.pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 28px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  gap: 24px;
}

/* 加载更多 */
.load-more-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.load-more-btn {
  min-width: 200px;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.load-more-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
}

/* 上传对话框 */
.upload-dialog-content {
  padding: 16px;
}

.upload-area {
  border-radius: 12px;
  overflow: hidden;
}

.modern-upload {
  border-radius: 12px;
}

.upload-content {
  padding: 48px;
  text-align: center;
}

.upload-icon {
  font-size: 4rem;
  color: var(--primary);
  margin-bottom: 24px;
}

.upload-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.upload-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
}

.upload-tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-manager-page {
    padding: 16px;
  }
  
  .welcome-banner {
    padding: 24px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .banner-title {
    font-size: 1.25rem !important;
  }
  
  .welcome-actions {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stats-card {
    padding: 10px;
    gap: 8px;
    min-height: 55px;
  }

  .stats-icon {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .stats-icon .el-icon {
    font-size: 0.9rem !important;
    width: 0.9rem !important;
    height: 0.9rem !important;
  }

  .stats-value {
    font-size: 1.3rem;
  }

  .stats-label {
    font-size: 0.75rem;
  }

  .stats-trend {
    font-size: 0.65rem;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .quick-action-card {
    padding: 16px;
    gap: 12px;
    min-height: 70px;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
    border-radius: 10px;
  }

  .action-content h3 {
    font-size: 0.9rem;
  }

  .action-content p {
    font-size: 0.8rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .browser-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .banner-title {
    font-size: 1.25rem !important;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .stats-card {
    padding: 8px;
    gap: 6px;
    min-height: 50px;
  }

  .stats-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .stats-icon .el-icon {
    font-size: 0.8rem !important;
    width: 0.8rem !important;
    height: 0.8rem !important;
  }

  .stats-value {
    font-size: 1.1rem;
  }

  .stats-label {
    font-size: 0.7rem;
  }

  .stats-trend {
    font-size: 0.6rem;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .quick-action-card {
    padding: 14px;
    gap: 10px;
    min-height: 60px;
  }

  .action-icon {
    width: 36px;
    height: 36px;
    font-size: 1rem;
    border-radius: 8px;
  }

  .action-content h3 {
    font-size: 0.85rem;
  }

  .action-content p {
    font-size: 0.75rem;
  }
   
  .welcome-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .welcome-actions .el-button {
    width: 100%;
  }
  
  .stats-card {
    padding: 16px;
  }
  
  .quick-action-card {
    padding: 8px;
    gap: 8px;
    min-height: 50px;
  }

  .action-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .action-content h3 {
    font-size: 0.8rem;
  }

  .action-content p {
    font-size: 0.65rem;
  }
  
  .file-grid {
    grid-template-columns: 1fr;
  }
}

/* 上传进度样式 */
.upload-progress-section {
  margin-top: 24px;
  padding: 20px;
  background: var(--bg-tertiary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
}

.progress-header {
  margin-bottom: 20px;
}

.progress-header h4 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.overall-progress {
  background: var(--bg-card);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.file-progress-list {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 16px;
  /* 添加硬件加速优化 */
  transform: translateZ(0);
  will-change: scroll-position;
}

.file-progress-list.collapsed {
  max-height: 200px;
}

/* 文件列表控制区域 */
.file-list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: var(--bg-card);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.file-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-item.success {
  color: #22c55e;
}

.stat-item.error {
  color: #ef4444;
}

.stat-item.uploading {
  color: var(--primary);
}

.list-toggle {
  flex-shrink: 0;
}

/* 折叠提示 */
.collapsed-hint {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  margin-top: 8px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px dashed var(--border-primary);
}

.hint-content {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.hint-content .el-icon {
  color: var(--text-tertiary);
}

.file-progress-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: var(--bg-card);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

.file-progress-item.uploading {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.05);
}

.file-progress-item.success {
  border-color: #22c55e;
  background: rgba(34, 197, 94, 0.05);
}

.file-progress-item.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.file-progress-item .file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-size: 1rem;
}

.file-progress-item.success .file-icon {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.file-progress-item.error .file-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.file-progress-item.uploading .file-icon {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-progress-item .file-size {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.file-status {
  flex: 0 0 120px;
  margin-left: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.status-text {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 12px;
  text-align: center;
}

.status-text.success {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
}

.status-text.uploading {
  color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
}

.error-message {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 4px;
  text-align: right;
}

.current-status {
  padding: 16px;
  background: var(--bg-card);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.status-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.upload-stats {
  display: flex;
  gap: 24px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
}

/* 统一的头部样式 */
.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.progress-header h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 文件操作区域样式 */
.file-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 200px;
}

.file-progress-item.ready {
  border-color: var(--border-primary);
  background: var(--bg-card);
}

.file-progress-item.ready:hover {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.05);
}

.file-progress-item.ready .file-icon {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

/* 批量搜索对话框样式 */
.batch-search-content {
  padding: 16px;
}

.search-instructions {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.search-instructions h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.search-instructions ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
}

.search-instructions li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.search-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding: 8px 12px;
  background: var(--bg-tertiary);
  border-radius: 6px;
  font-size: 0.875rem;
}

.error-text {
  color: #ef4444;
  font-weight: 500;
}

.keywords-preview {
  margin-top: 16px;
  padding: 16px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.keywords-preview h5 {
  margin: 0 0 12px 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-container {
  display: flex;
  gap: 12px;
  align-items: center;
}
/* 上传中视图样式 */
.uploading-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.overall-progress {
  background: white;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.files-count {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.main-progress {
  margin-top: 8px;
}

.current-status {
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.current-file {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 500;
  color: #1e293b;
}

.upload-stats {
  display: flex;
  gap: 24px;
  font-size: 0.875rem;
  color: #64748b;
}

.upload-summary {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.summary-item.success {
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.2);
  background: rgba(34, 197, 94, 0.05);
}

.summary-item.error {
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.2);
  background: rgba(239, 68, 68, 0.05);
}

.summary-item.uploading {
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.2);
  background: rgba(59, 130, 246, 0.05);
}

.file-selection-view {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .uploading-view {
    gap: 16px;
  }
  
  .overall-progress {
    padding: 20px;
  }
  
  .progress-text {
    font-size: 1rem;
  }
  
  .current-status {
    padding: 16px;
  }
  
  .upload-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .upload-summary {
    gap: 12px;
  }
  
  .summary-item {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .overall-progress {
    padding: 16px;
  }
  
  .progress-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .progress-text {
    font-size: 0.95rem;
  }
  
  .files-count {
    font-size: 0.8rem;
  }
  
  .current-file {
    font-size: 0.9rem;
  }
  
  .upload-summary {
    flex-direction: column;
    gap: 8px;
  }
  
  .summary-item {
    justify-content: center;
  }
}
</style>