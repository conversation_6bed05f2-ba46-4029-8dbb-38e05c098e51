package utils

import (
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// 统一JWT密钥
var jwtSecret = []byte("your-jwt-secret")

// GenerateJWT 生成JWT令牌
func GenerateJWT(userID uint, username string) (string, error) {
	// 使用MapClaims确保与中间件解析一致
	claims := jwt.MapClaims{
		"user_id":  userID,
		"username": username,
		"exp":      time.Now().Add(24 * time.Hour).Unix(),
		"iat":      time.Now().Unix(),
		"nbf":      time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtSecret)
}

// ParseJWT 解析JWT令牌
func ParseJWT(tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, jwt.ErrInvalidKey
}

// ValidateJWT 验证JWT令牌
func ValidateJWT(tokenString string) bool {
	_, err := ParseJWT(tokenString)
	return err == nil
}

// GetJWTSecret 获取JWT密钥（供其他包使用）
func GetJWTSecret() string {
	return string(jwtSecret)
}
